import React from "react";
import { render, screen } from "@testing-library/react";
import ChooseRoleClient from "@/app/(auth)/choose-role/ChooseRoleClient";
import { useSearchParams } from "next/navigation";

jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
  useSearchParams: jest.fn(),
}));

jest.mock("sonner", () => ({
  toast: {
    error: jest.fn(),
  },
}));

jest.mock("@/app/(auth)/choose-role/actions", () => ({
  createCustomerProfile: jest.fn().mockResolvedValue({ success: true }),
}));

describe("ChooseRoleClient Responsive Design", () => {
  beforeEach(() => {
    (useSearchParams as jest.Mock).mockReturnValue(new URLSearchParams());
  });

  it("should have responsive classes for different screen sizes", () => {
    const { container } = render(<ChooseRoleClient userId="test-user" />);

    // Check for responsive padding and margin classes
    const mainContainer = container.firstChild;
    expect(mainContainer).toHaveClass(
      "p-2 sm:p-4 pt-6 pb-20 md:pb-6"
    );

    // Check for responsive text size classes
    const title = screen.getByText("Choose Your Role");
    expect(title).toHaveClass("text-xl sm:text-2xl md:text-3xl");

    const description = screen.getByText(
      "Select how you'll be using our platform. This is a one-time setup that cannot be changed later."
    );
    expect(description).toHaveClass("text-xs sm:text-sm md:text-base");

    // Check for responsive button padding
    const customerButton = screen.getByText("As a Customer").closest("button");
    expect(customerButton).toHaveClass("py-2 sm:py-3 md:py-6 px-2 sm:px-3 md:px-5");
  });
});