"use server";

import { createCustomerProfile } from "@/app/(auth)/choose-role/actions";
import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
import { revalidatePath } from "next/cache";

jest.mock("@/utils/supabase/server");
jest.mock("next/navigation", () => ({
  redirect: jest.fn().mockImplementation((url) => {
    throw new Error(`REDIRECT: ${url}`);
  }),
}));
jest.mock("next/cache", () => ({
  revalidatePath: jest.fn(),
}));

describe("Choose Role Server Action - Next.js Tests", () => {
  let mockSupabase: any;

  beforeEach(() => {
    jest.clearAllMocks();

    // Create a proper query builder mock
    const queryBuilder = {
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      maybeSingle: jest.fn(),
      insert: jest.fn(),
    };

    mockSupabase = {
      auth: {
        getUser: jest.fn(),
      },
      from: jest.fn(() => queryBuilder),
    };
    (createClient as jest.Mock).mockReturnValue(mockSupabase);
  });

  it("should call redirect to the customer dashboard if no redirectSlug is provided", async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: "new-user", email: "<EMAIL>" } },
      error: null,
    });

    const queryBuilder = mockSupabase.from();
    queryBuilder.maybeSingle.mockResolvedValue({ data: null, error: null }); // No existing profile
    queryBuilder.insert.mockResolvedValue({ error: null });

    await expect(createCustomerProfile("new-user")).rejects.toThrow("REDIRECT: /dashboard/customer");

    expect(redirect).toHaveBeenCalledWith("/dashboard/customer");
    expect(redirect).toHaveBeenCalledTimes(1);
  });

  it("should call redirect with the provided redirectSlug", async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: "new-user", email: "<EMAIL>" } },
      error: null,
    });

    const queryBuilder = mockSupabase.from();
    queryBuilder.maybeSingle.mockResolvedValue({ data: null, error: null });
    queryBuilder.insert.mockResolvedValue({ error: null });

    await expect(createCustomerProfile("new-user", "some-business-slug")).rejects.toThrow("REDIRECT: /some-business-slug");

    expect(redirect).toHaveBeenCalledWith("/some-business-slug");
    expect(redirect).toHaveBeenCalledTimes(1);
  });

  it("should call revalidatePath for relevant paths on successful profile creation", async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: "new-user", email: "<EMAIL>" } },
      error: null,
    });

    const queryBuilder = mockSupabase.from();
    queryBuilder.maybeSingle.mockResolvedValue({ data: null, error: null });
    queryBuilder.insert.mockResolvedValue({ error: null });

    await expect(createCustomerProfile("new-user")).rejects.toThrow("REDIRECT: /dashboard/customer");

    expect(revalidatePath).toHaveBeenCalledWith("/choose-role");
    expect(revalidatePath).toHaveBeenCalledWith("/dashboard/customer");
    expect(revalidatePath).toHaveBeenCalledTimes(2);
  });

  it("should redirect an existing user without creating a new profile", async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: "existing-user", email: "<EMAIL>" } },
      error: null,
    });

    const queryBuilder = mockSupabase.from();
    queryBuilder.maybeSingle.mockResolvedValue({
      data: { id: "existing-user" },
      error: null,
    });

    await expect(createCustomerProfile("existing-user", "another-slug")).rejects.toThrow("REDIRECT: /another-slug");

    expect(queryBuilder.insert).not.toHaveBeenCalled();
    expect(redirect).toHaveBeenCalledWith("/another-slug");
  });
});