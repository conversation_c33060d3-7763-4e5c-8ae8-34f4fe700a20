import type { Config } from 'jest';
import nextJest from 'next/jest.js';

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files in your test environment
  dir: './',
});

// Add any custom config to be passed to Jest
const config: Config = {
  transformIgnorePatterns: [
    "/node_modules/(?!(@supabase/ssr|@supabase/auth-helpers-nextjs|@supabase/realtime-js|@supabase/supabase-js|@radix-ui|@dnd-kit|@testing-library|framer-motion|lucide-react|sonner|next-intl|use-intl|uncrypto|@upstash|uuid|@upstash/redis)/)",
  ],
  testPathIgnorePatterns: ["/node_modules/", "/.next/", "/e2e/"],
  coverageProvider: 'v8',
  testEnvironment: 'jsdom',
  // Add more setup options before each test is run
  setupFilesAfterEnv: ['<rootDir>/jest.setup.ts'],
  moduleNameMapper: {
    // Handle module aliases
    '^@/components/(.*)$': '<rootDir>/components/$1',
    '^@/lib/(.*)$': '<rootDir>/lib/$1',
    '^@/hooks/(.*)$': '<rootDir>/hooks/$1',
    '^@/utils/(.*)$': '<rootDir>/utils/$1',
    '^@/app/(.*)$': '<rootDir>/app/$1',
    '^@/types/(.*)$': '<rootDir>/types/$1',
    '^@/(.*)$': '<rootDir>/$1',
    // Mock Supabase modules
    '^@supabase/ssr$': '<rootDir>/__mocks__/supabase-ssr.js',
    '^@supabase/auth-helpers-nextjs$': '<rootDir>/__mocks__/supabase-auth-helpers-nextjs.js',
    '^@supabase/realtime-js$': '<rootDir>/__mocks__/supabase-realtime.js',
    '^@supabase/supabase-js$': '<rootDir>/__mocks__/supabase-js.js',
    // Mock next-intl modules
    '^next-intl$': '<rootDir>/__mocks__/next-intl.js',
    '^use-intl$': '<rootDir>/__mocks__/use-intl.js',
    // Handle CSS imports
    '^.+\\.(css|sass|scss)$': 'identity-obj-proxy',
    // Handle image imports
    '^.+\\.(jpg|jpeg|png|gif|webp|svg)$': '<rootDir>/__mocks__/fileMock.js',
  },
  // Automatically clear mock calls, instances, contexts and results before every test
  clearMocks: true,
  // Handle ES modules
  extensionsToTreatAsEsm: ['.ts', '.tsx'],
  globals: {
    'ts-jest': {
      useESM: true,
    },
  },
  // Collect coverage from these files
  collectCoverageFrom: [
    'app/**/*.{js,jsx,ts,tsx}',
    'components/**/*.{js,jsx,ts,tsx}',
    'lib/**/*.{js,jsx,ts,tsx}',
    'utils/**/*.{js,jsx,ts,tsx}',
    '!**/*.d.ts',
    '!**/node_modules/**',
  ],
};

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
export default createJestConfig(config);