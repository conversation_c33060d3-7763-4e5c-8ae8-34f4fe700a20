import { sendOTP, verifyOTP, loginWithMobilePassword } from '@/app/(main)/login/actions';
import { createClient } from '@/utils/supabase/client';

// Mock the Supabase client
jest.mock('@/utils/supabase/client', () => ({
  createClient: jest.fn(),
}));

describe('Auth API Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('sendOTP', () => {
    it('should return success when a valid email is provided', async () => {
      const mockSupabase = {
        auth: {
          signInWithOtp: jest.fn().mockResolvedValue({ error: null }),
        },
      };
      (createClient as jest.Mock).mockReturnValue(mockSupabase);

      const result = await sendOTP({ email: '<EMAIL>' });

      expect(result.success).toBe(true);
      expect(result.message).toBe('OTP sent to your email address. Please check your inbox.');
      expect(mockSupabase.auth.signInWithOtp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        options: {
          shouldCreateUser: true,
          data: {
            auth_type: 'email',
          },
        },
      });
    });

    it('should return a validation error for an invalid email', async () => {
      const result = await sendOTP({ email: 'invalid-email' });
      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid email address');
    });

    it('should handle Supabase rate limit errors gracefully', async () => {
      const rateLimitError = {
        message: 'Email rate limit exceeded',
        __isAuthError: true,
        status: 429,
      };
      const mockSupabase = {
        auth: {
          signInWithOtp: jest.fn().mockResolvedValue({ error: rateLimitError }),
        },
      };
      (createClient as jest.Mock).mockReturnValue(mockSupabase);

      const result = await sendOTP({ email: '<EMAIL>' });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Email rate limit exceeded');
      expect(result.isConfigurationError).toBe(true);
    });

    it('should handle other Supabase auth errors', async () => {
      const authError = {
        message: 'An unexpected error occurred',
        __isAuthError: true,
        status: 500,
      };
      const mockSupabase = {
        auth: {
          signInWithOtp: jest.fn().mockResolvedValue({ error: authError }),
        },
      };
      (createClient as jest.Mock).mockReturnValue(mockSupabase);

      const result = await sendOTP({ email: '<EMAIL>' });

      expect(result.success).toBe(false);
      expect(result.error).toBe('An unexpected error occurred');
    });
  });

  describe('verifyOTP', () => {
    it('should return success when a valid OTP is provided', async () => {
      const mockSupabase = {
        auth: {
          verifyOtp: jest.fn().mockResolvedValue({ data: { user: { id: 'user-123' } }, error: null }),
        },
      };
      (createClient as jest.Mock).mockReturnValue(mockSupabase);

      const result = await verifyOTP({ email: '<EMAIL>', token: '123456' });

      expect(result.success).toBe(true);
      expect(result.message).toBe('Successfully signed in!');
      expect(mockSupabase.auth.verifyOtp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        token: '123456',
        type: 'email',
      });
    });

    it('should return a validation error for an invalid OTP', async () => {
      const result = await verifyOTP({ email: '<EMAIL>', token: '123' });
      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid OTP format');
    });

    it('should handle Supabase auth errors during OTP verification', async () => {
      const authError = {
        message: 'Invalid OTP',
        __isAuthError: true,
        status: 400,
      };
      const mockSupabase = {
        auth: {
          verifyOtp: jest.fn().mockResolvedValue({ error: authError }),
        },
      };
      (createClient as jest.Mock).mockReturnValue(mockSupabase);

      const result = await verifyOTP({ email: '<EMAIL>', token: '111111' });

      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid OTP');
    });
  });

  describe('loginWithMobilePassword', () => {
    it('should return success with valid mobile and password', async () => {
      const mockSupabase = {
        auth: {
          signInWithPassword: jest.fn().mockResolvedValue({ data: { user: { id: 'user-123' } }, error: null }),
        },
      };
      (createClient as jest.Mock).mockReturnValue(mockSupabase);

      const result = await loginWithMobilePassword({ mobile: '**********', password: 'password123' });

      expect(result.success).toBe(true);
      expect(result.message).toBe('Successfully signed in!');
      expect(mockSupabase.auth.signInWithPassword).toHaveBeenCalledWith({
        phone: '+91**********',
        password: 'password123',
      });
    });

    it('should return a validation error for an invalid mobile number', async () => {
      const result = await loginWithMobilePassword({ mobile: '123', password: 'password123' });
      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid mobile number or password format');
    });

    it('should handle Supabase auth errors for invalid credentials', async () => {
      const authError = {
        message: 'Invalid login credentials',
        __isAuthError: true,
        status: 400,
      };
      const mockSupabase = {
        auth: {
          signInWithPassword: jest.fn().mockResolvedValue({ error: authError }),
        },
      };
      (createClient as jest.Mock).mockReturnValue(mockSupabase);

      const result = await loginWithMobilePassword({ mobile: '**********', password: 'wrongpassword' });

      expect(result.success).toBe(false);
      expect(result.error).toBe('Invalid login credentials');
    });
  });

  describe('Social Login', () => {
    it('should return the OAuth URL for Google social login', async () => {
      const mockSupabase = {
        auth: {
          signInWithOAuth: jest.fn().mockResolvedValue({
            data: { url: 'https://supabase.io/auth/v1/authorize?provider=google' },
            error: null,
          }),
        },
      };
      (createClient as jest.Mock).mockReturnValue(mockSupabase);

      // This is a placeholder for where the client-side social login would be initiated.
      // In a real scenario, this would be a client-side function.
      // For this integration test, we'll just check if the mock is called correctly.
      const socialLogin = async (provider: 'google') => {
        const supabase = createClient();
        return await supabase.auth.signInWithOAuth({
          provider,
          options: {
            redirectTo: 'http://localhost:3000/auth/callback',
          },
        });
      };

      const result = await socialLogin('google');

      expect(result.data.url).toBe('https://supabase.io/auth/v1/authorize?provider=google');
      expect(mockSupabase.auth.signInWithOAuth).toHaveBeenCalledWith({
        provider: 'google',
        options: {
          redirectTo: 'http://localhost:3000/auth/callback',
        },
      });
    });

    it('should handle errors during social login', async () => {
      const authError = {
        message: 'Social login failed',
        __isAuthError: true,
        status: 500,
      };
      const mockSupabase = {
        auth: {
          signInWithOAuth: jest.fn().mockResolvedValue({ error: authError }),
        },
      };
      (createClient as jest.Mock).mockReturnValue(mockSupabase);

      const socialLogin = async (provider: 'google') => {
        const supabase = createClient();
        return await supabase.auth.signInWithOAuth({ provider });
      };

      const result = await socialLogin('google');
      expect(result.error).toBe(authError);
    });
  });

  describe('Network and Timeout Errors', () => {
    it('should handle network errors during OTP sending', async () => {
      const mockSupabase = {
        auth: {
          signInWithOtp: jest.fn().mockRejectedValue(new Error('Network error')),
        },
      };
      (createClient as jest.Mock).mockReturnValue(mockSupabase);

      const result = await sendOTP({ email: '<EMAIL>' });

      expect(result.success).toBe(false);
      expect(result.error).toBe('An unexpected error occurred. Please try again.');
    });

    it('should handle network errors during OTP verification', async () => {
      const mockSupabase = {
        auth: {
          verifyOtp: jest.fn().mockRejectedValue(new Error('Network error')),
        },
      };
      (createClient as jest.Mock).mockReturnValue(mockSupabase);

      const result = await verifyOTP({ email: '<EMAIL>', token: '123456' });

      expect(result.success).toBe(false);
      expect(result.error).toBe('An unexpected error occurred. Please try again.');
    });

    it('should handle network errors during mobile/password login', async () => {
      const mockSupabase = {
        auth: {
          signInWithPassword: jest.fn().mockRejectedValue(new Error('Network error')),
        },
      };
      (createClient as jest.Mock).mockReturnValue(mockSupabase);

      const result = await loginWithMobilePassword({ mobile: '**********', password: 'password123' });

      expect(result.success).toBe(false);
      expect(result.error).toBe('An unexpected error occurred. Please try again.');
    });
  });
});