import { updateProductVariant, updateMultipleVariants } from '@/app/(dashboard)/dashboard/business/products/actions/updateVariant';
import { createClient } from '@/utils/supabase/server';
import { revalidatePath } from 'next/cache';

// Mock dependencies
jest.mock('@/utils/supabase/server', () => ({
  createClient: jest.fn(),
}));
jest.mock('next/cache', () => ({
  revalidatePath: jest.fn(),
}));

const mockSupabase = createClient as jest.Mock;

describe('Product Variant Actions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('updateProductVariant', () => {
    it('should return an error if the user is not authenticated', async () => {
      // Arrange
      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ error: new Error('Auth error'), data: { user: null } }),
        },
      });
      const formData = new FormData();

      // Act
      const result = await updateProductVariant('var-123', formData);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('User not authenticated.');
    });

    it('should update a variant successfully', async () => {
      // Arrange
      const mockUser = { id: 'user-123' };
      const mockVariant = { id: 'var-123', product_id: 'prod-123', products_services: { business_id: 'user-123' } };
      const mockUpdate = jest.fn().mockReturnThis();
      const mockEq = jest.fn().mockReturnThis();
      const mockSelect = jest.fn().mockReturnThis();
      const mockSingle = jest.fn().mockResolvedValue({ data: mockVariant, error: null });

      mockSupabase.mockReturnValue({
        auth: {
          getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
        },
        from: jest.fn(() => ({
          update: mockUpdate,
          eq: mockEq,
          select: mockSelect,
          single: mockSingle,
        })),
      });

      const formData = new FormData();
      formData.append('variant_name', 'New Name');
      formData.append('base_price', '200');

      // Act
      const result = await updateProductVariant('var-123', formData);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data?.id).toBe('var-123');
      expect(revalidatePath).toHaveBeenCalledWith('/dashboard/business/products');
    });
  });

  describe('updateMultipleVariants', () => {
    it('should update multiple variants successfully', async () => {
        // Arrange
        const mockUser = { id: 'user-123' };
        const mockVariant = { id: 'var-123', products_services: { business_id: 'user-123' } };
        const mockUpdate = jest.fn().mockResolvedValue({ error: null });
        const mockEq = jest.fn().mockReturnThis();
        const mockSelect = jest.fn().mockReturnThis();
        const mockSingle = jest.fn().mockResolvedValue({ data: mockVariant, error: null });
  
        mockSupabase.mockReturnValue({
          auth: {
            getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
          },
          from: jest.fn(() => ({
            update: mockUpdate,
            eq: mockEq,
            select: mockSelect,
            single: mockSingle,
          })),
        });
  
        const updates = [
          { id: 'var-1', data: { base_price: 110 } },
          { id: 'var-2', data: { is_available: false } },
        ];
  
        // Act
        const result = await updateMultipleVariants(updates);
  
        // Assert
        expect(result.success).toBe(true);
        expect(result.updated_count).toBe(2);
        expect(revalidatePath).toHaveBeenCalledWith('/dashboard/business/products');
      });
  });
});