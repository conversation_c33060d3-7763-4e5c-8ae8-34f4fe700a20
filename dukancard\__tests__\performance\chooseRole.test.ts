import { createCustomerProfile } from "@/app/(auth)/choose-role/actions";
import ChooseRolePage from "@/app/(auth)/choose-role/page";
import { createClient } from "@/utils/supabase/server";
import { performance } from "perf_hooks";

// Mocking Supabase and Next.js utilities
jest.mock("@/utils/supabase/server", () => ({
  createClient: jest.fn(),
}));

jest.mock("next/navigation", () => ({
  redirect: jest.fn(),
}));

jest.mock("next/cache", () => ({
  revalidatePath: jest.fn(),
}));

const mockSupabase = createClient as jest.Mock;

describe("Choose Role - Performance Tests", () => {

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Page Load Performance", () => {
    it("should render the ChooseRolePage within an acceptable time limit", async () => {
      const mockUser = { id: "test-user", user_metadata: {}, email: "<EMAIL>" };
      const mockGetUser = jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null });
      const mockFrom = jest.fn().mockReturnThis();
      const mockSelect = jest.fn().mockReturnThis();
      const mockEq = jest.fn().mockResolvedValue({ data: null, error: null });

      mockSupabase.mockReturnValue({
        auth: { getUser: mockGetUser },
        from: mockFrom,
        select: mockSelect,
        eq: mockEq,
      });

      const startTime = performance.now();
      // @ts-ignore
      await ChooseRolePage({ searchParams: Promise.resolve({}) });
      const endTime = performance.now();
      const renderTime = endTime - startTime;

      console.log(`ChooseRolePage render time: ${renderTime.toFixed(2)}ms`);
      expect(renderTime).toBeLessThan(500); // Set a reasonable threshold
    });
  });

  describe("createCustomerProfile Server Action Performance", () => {
    it("should execute within an acceptable time limit", async () => {
      const userId = "performance-test-user";
      const mockUser = { id: userId, user_metadata: { full_name: "Perf User" }, email: "<EMAIL>" };
      const mockGetUser = jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null });
      const mockFrom = jest.fn().mockReturnThis();
      const mockSelect = jest.fn().mockReturnThis();
      const mockEq = jest.fn().mockResolvedValue({ data: null, error: null });
      const mockInsert = jest.fn().mockResolvedValue({ error: null });

      mockSupabase.mockReturnValue({
        auth: { getUser: mockGetUser },
        from: mockFrom,
        select: mockSelect,
        eq: mockEq,
        insert: mockInsert,
      });

      const startTime = performance.now();
      await createCustomerProfile(userId, null, null);
      const endTime = performance.now();
      const executionTime = endTime - startTime;

      console.log(`createCustomerProfile execution time: ${executionTime.toFixed(2)}ms`);
      expect(executionTime).toBeLessThan(300); // Set a reasonable threshold
    });
  });
});
  describe("Concurrent User Scenarios", () => {
    it("should handle concurrent requests to create a profile for the same user gracefully", async () => {
      const userId = "concurrent-user";
      const mockUser = { id: userId, user_metadata: { full_name: "Concurrent User" }, email: "<EMAIL>" };
      const mockGetUser = jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null });
      
      // Simulate a race condition where the profile doesn't exist on the first check
      const mockSelect = jest.fn().mockResolvedValueOnce({ data: null, error: null });
      
      // The insert should only succeed once. Subsequent attempts should fail due to unique constraint.
      const mockInsert = jest.fn()
        .mockResolvedValueOnce({ error: null }) // First call succeeds
        .mockResolvedValue({ error: { message: "unique constraint violation", code: "23505" } }); // Subsequent calls fail

      const mockFrom = jest.fn().mockReturnThis();
      const mockEq = jest.fn().mockReturnThis();

      mockSupabase.mockReturnValue({
        auth: { getUser: mockGetUser },
        from: mockFrom,
        select: mockSelect,
        eq: mockEq,
        insert: mockInsert,
      });

      // Simulate 5 concurrent requests
      const promises = Array(5).fill(0).map(() => createCustomerProfile(userId, null, null));
      const results = await Promise.all(promises);

      const successfulCreations = results.filter(r => r === undefined); // Successful redirect returns undefined
      const failedCreations = results.filter(r => r && r.error);

      // Only one request should succeed in creating the profile and redirecting.
      // The others should fail gracefully.
      expect(successfulCreations.length).toBe(1);
      expect(failedCreations.length).toBe(4);
      expect(mockInsert).toHaveBeenCalledTimes(5);
    });
  });