import { createCustomerProfile } from "@/app/(auth)/choose-role/actions";
import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
import { revalidatePath } from "next/cache";

// Mock dependencies
jest.mock("@/utils/supabase/server", () => ({
  createClient: jest.fn(),
}));

jest.mock("next/navigation", () => ({
  redirect: jest.fn().mockImplementation((url) => {
    throw new Error(`REDIRECT: ${url}`);
  }),
}));

jest.mock("next/cache", () => ({
  revalidatePath: jest.fn(),
}));

// Create a shared query builder instance
let mockQueryBuilder: any;

const mockSupabase = {
  auth: {
    getUser: jest.fn(),
  },
  from: jest.fn(() => mockQueryBuilder),
};

(createClient as jest.Mock).mockReturnValue(mockSupabase);

describe("createCustomerProfile", () => {
  const userId = "test-user-id";
  const user = {
    id: userId,
    user_metadata: { full_name: "Test User" },
    email: "<EMAIL>",
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Reset the query builder for each test
    mockQueryBuilder = {
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      maybeSingle: jest.fn(),
      insert: jest.fn(),
    };
  });

  it("should return an error if userId is not provided", async () => {
    const result = await createCustomerProfile("");
    expect(result).toEqual({ error: "User ID is required." });
  });

  it("should return an error if user is not found", async () => {
    mockSupabase.auth.getUser.mockResolvedValueOnce({ data: { user: null }, error: null });
    const result = await createCustomerProfile(userId);
    expect(result).toEqual({ error: "User not found or authentication error." });
  });

  it("should return an error if there is a database error checking for an existing profile", async () => {
    mockSupabase.auth.getUser.mockResolvedValueOnce({ data: { user }, error: null });
    mockQueryBuilder.maybeSingle.mockResolvedValueOnce({ data: null, error: new Error("DB Error") });
    const result = await createCustomerProfile(userId);
    expect(result).toEqual({ error: "Database error checking profile." });
  });

  it("should redirect to customer dashboard if profile already exists", async () => {
    mockSupabase.auth.getUser.mockResolvedValueOnce({ data: { user }, error: null });
    mockQueryBuilder.maybeSingle.mockResolvedValueOnce({ data: { id: userId }, error: null });
    await expect(createCustomerProfile(userId)).rejects.toThrow("REDIRECT: /dashboard/customer");
    expect(redirect).toHaveBeenCalledWith("/dashboard/customer");
  });

  it("should redirect to the provided slug if profile already exists", async () => {
    mockSupabase.auth.getUser.mockResolvedValueOnce({ data: { user }, error: null });
    mockQueryBuilder.maybeSingle.mockResolvedValueOnce({ data: { id: userId }, error: null });
    await expect(createCustomerProfile(userId, "test-slug")).rejects.toThrow("REDIRECT: /test-slug");
    expect(redirect).toHaveBeenCalledWith("/test-slug");
  });

  it("should return an error if profile creation fails", async () => {
    mockSupabase.auth.getUser.mockResolvedValueOnce({ data: { user }, error: null });
    mockQueryBuilder.maybeSingle.mockResolvedValueOnce({ data: null, error: null });
    mockQueryBuilder.insert.mockResolvedValueOnce({ error: new Error("Insert failed") });
    const result = await createCustomerProfile(userId);
    expect(result).toEqual({ error: "Failed to create profile." });
  });

  it("should create a profile and redirect to customer dashboard", async () => {
    mockSupabase.auth.getUser.mockResolvedValueOnce({ data: { user }, error: null });
    mockQueryBuilder.maybeSingle.mockResolvedValueOnce({ data: null, error: null });
    mockQueryBuilder.insert.mockResolvedValueOnce({ error: null });

    await expect(createCustomerProfile(userId)).rejects.toThrow("REDIRECT: /dashboard/customer");

    expect(mockSupabase.from).toHaveBeenCalledWith("customer_profiles");
    expect(mockQueryBuilder.insert).toHaveBeenCalledWith({
      id: userId,
      name: "Test User",
      email: "<EMAIL>",
    });
    expect(revalidatePath).toHaveBeenCalledWith("/choose-role");
    expect(revalidatePath).toHaveBeenCalledWith("/dashboard/customer");
    expect(redirect).toHaveBeenCalledWith("/dashboard/customer");
  });

  it("should create a profile and redirect to the provided slug", async () => {
    mockSupabase.auth.getUser.mockResolvedValueOnce({ data: { user }, error: null });
    mockQueryBuilder.maybeSingle.mockResolvedValueOnce({ data: null, error: null });
    mockQueryBuilder.insert.mockResolvedValueOnce({ error: null });

    await expect(createCustomerProfile(userId, "test-slug")).rejects.toThrow("REDIRECT: /test-slug");

    expect(redirect).toHaveBeenCalledWith("/test-slug");
  });
});