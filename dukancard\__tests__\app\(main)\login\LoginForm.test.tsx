import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import { useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import { LoginForm } from '@/app/(main)/login/LoginForm';
import * as actions from '@/app/(main)/login/actions';

// Mock the next/navigation module
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}));

// Mock the sonner toast library
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
  },
}));

// Mock the server actions
jest.mock('@/app/(main)/login/actions', () => ({
  sendOTP: jest.fn(),
  verifyOTP: jest.fn(),
  loginWithMobilePassword: jest.fn(),
}));

describe('LoginForm', () => {
  let mockRouter: { push: jest.Mock };
  let mockSearchParams: { get: jest.Mock };

  beforeEach(() => {
    mockRouter = {
      push: jest.fn(),
    };
    mockSearchParams = {
      get: jest.fn(),
    };
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useSearchParams as jest.Mock).mockReturnValue(mockSearchParams);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders the email OTP form by default', () => {
    render(<LoginForm />);
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /continue/i })).toBeInTheDocument();
  });

  it('shows an error message for invalid email', async () => {
    render(<LoginForm />);
    fireEvent.change(screen.getByLabelText(/email address/i), { target: { value: 'invalid-email' } });
    fireEvent.click(screen.getByRole('button', { name: /continue/i }));
    await waitFor(() => {
      expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument();
    });
  });

  it('proceeds to OTP step on successful email submission', async () => {
    (actions.sendOTP as jest.Mock).mockResolvedValue({ success: true, message: 'OTP sent' });
    render(<LoginForm />);
    fireEvent.change(screen.getByLabelText(/email address/i), { target: { value: '<EMAIL>' } });
    fireEvent.click(screen.getByRole('button', { name: /continue/i }));
    await waitFor(() => {
      expect(screen.getByText(/enter verification code/i)).toBeInTheDocument();
    });
  });

  it('shows an error toast on failed email submission', async () => {
    (actions.sendOTP as jest.Mock).mockResolvedValue({ success: false, error: 'Failed to send OTP' });
    render(<LoginForm />);
    fireEvent.change(screen.getByLabelText(/email address/i), { target: { value: '<EMAIL>' } });
    fireEvent.click(screen.getByRole('button', { name: /continue/i }));
    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Failed to send OTP', { description: 'Failed to send OTP' });
    });
  });

  it('handles OTP submission', async () => {
    (actions.sendOTP as jest.Mock).mockResolvedValue({ success: true, message: 'OTP sent' });
    (actions.verifyOTP as jest.Mock).mockResolvedValue({ success: true, message: 'Success' });
    render(<LoginForm />);
    fireEvent.change(screen.getByLabelText(/email address/i), { target: { value: '<EMAIL>' } });
    fireEvent.click(screen.getByRole('button', { name: /continue/i }));
    await waitFor(() => {
      fireEvent.change(screen.getByLabelText(/enter verification code/i), { target: { value: '123456' } });
      fireEvent.click(screen.getByRole('button', { name: /verify & sign in/i }));
    });
    await waitFor(() => {
      expect(toast.success).toHaveBeenCalledWith('Sign in successful!', { description: 'Redirecting to your dashboard...' });
    });
  });

  it('switches to mobile password form', () => {
    render(<LoginForm />);
    fireEvent.click(screen.getByRole('button', { name: /mobile \+ password/i }));
    expect(screen.getByLabelText(/mobile number/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
  });

  it('handles mobile password login', async () => {
    (actions.loginWithMobilePassword as jest.Mock).mockResolvedValue({ success: true, message: 'Success' });
    render(<LoginForm />);
    fireEvent.click(screen.getByRole('button', { name: /mobile \+ password/i }));
    fireEvent.change(screen.getByLabelText(/mobile number/i), { target: { value: '9876543210' } });
    fireEvent.change(screen.getByLabelText(/password/i), { target: { value: 'password' } });
    fireEvent.click(screen.getByRole('button', { name: /sign in/i }));
    await waitFor(() => {
      expect(toast.success).toHaveBeenCalledWith('Sign in successful!', { description: 'Redirecting to your dashboard...' });
    });
  });
});