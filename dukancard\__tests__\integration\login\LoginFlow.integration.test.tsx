import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { LoginForm } from "@/app/(main)/login/LoginForm";
import * as actions from "@/app/(main)/login/actions";
import * as redirectActions from "@/lib/actions/redirectAfterLogin";
import { createClient } from "@/utils/supabase/client";

// Mock dependencies
jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}));

jest.mock("sonner", () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

jest.mock("@/app/(main)/login/actions", () => ({
  sendOTP: jest.fn(),
  verifyOTP: jest.fn(),
  loginWithMobilePassword: jest.fn(),
}));

jest.mock("@/lib/actions/redirectAfterLogin", () => ({
  getPostLoginRedirectPath: jest.fn(),
}));

jest.mock("@/utils/supabase/client", () => ({
  createClient: jest.fn(),
}));

const mockRouter = {
  push: jest.fn(),
};
const mockSearchParams = new URLSearchParams();

describe("LoginFlow Integration Tests", () => {
  beforeEach(() => {
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useSearchParams as jest.Mock).mockReturnValue(mockSearchParams);
    (createClient as jest.Mock).mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user: { id: "user-123" } },
        }),
      },
    });
    jest.clearAllMocks();
  });

  it("should render the email OTP form by default", () => {
    render(<LoginForm />);
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByRole("button", { name: /send otp/i })).toBeInTheDocument();
  });

  it("should handle successful email OTP login flow", async () => {
    (actions.sendOTP as jest.Mock).mockResolvedValue({ success: true, message: "OTP sent" });
    (actions.verifyOTP as jest.Mock).mockResolvedValue({ success: true });
    (redirectActions.getPostLoginRedirectPath as jest.Mock).mockResolvedValue("/dashboard");

    render(<LoginForm />);

    // Step 1: Enter email and send OTP
    fireEvent.change(screen.getByLabelText(/email/i), { target: { value: "<EMAIL>" } });
    fireEvent.click(screen.getByRole("button", { name: /send otp/i }));

    await waitFor(() => {
      expect(actions.sendOTP).toHaveBeenCalledWith({ email: "<EMAIL>" });
      expect(toast.success).toHaveBeenCalledWith("OTP sent!", { description: "OTP sent" });
    });

    // Step 2: Enter OTP and verify
    await waitFor(() => {
      expect(screen.getByLabelText(/one-time password/i)).toBeInTheDocument();
    });
    fireEvent.change(screen.getByLabelText(/one-time password/i), { target: { value: "123456" } });
    fireEvent.click(screen.getByRole("button", { name: /verify otp/i }));

    await waitFor(() => {
      expect(actions.verifyOTP).toHaveBeenCalledWith({ email: "<EMAIL>", token: "123456" });
      expect(toast.success).toHaveBeenCalledWith("Sign in successful!", { description: "Redirecting to your dashboard..." });
      expect(mockRouter.push).toHaveBeenCalledWith("/dashboard");
    });
  });

  it("should show an error message for incorrect OTP", async () => {
    (actions.sendOTP as jest.Mock).mockResolvedValue({ success: true, message: "OTP sent" });
    (actions.verifyOTP as jest.Mock).mockResolvedValue({ success: false, error: "Invalid OTP" });

    render(<LoginForm />);

    // Step 1: Enter email and send OTP
    fireEvent.change(screen.getByLabelText(/email/i), { target: { value: "<EMAIL>" } });
    fireEvent.click(screen.getByRole("button", { name: /send otp/i }));

    await waitFor(() => expect(screen.getByLabelText(/one-time password/i)).toBeInTheDocument());

    // Step 2: Enter incorrect OTP
    fireEvent.change(screen.getByLabelText(/one-time password/i), { target: { value: "654321" } });
    fireEvent.click(screen.getByRole("button", { name: /verify otp/i }));

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith("OTP verification failed", { description: "Invalid OTP" });
      expect(mockRouter.push).not.toHaveBeenCalled();
    });
  });

  it("should switch to the mobile/password form and handle successful login", async () => {
    (actions.loginWithMobilePassword as jest.Mock).mockResolvedValue({ success: true });
    (redirectActions.getPostLoginRedirectPath as jest.Mock).mockResolvedValue("/dashboard");

    render(<LoginForm />);

    // Switch to mobile/password form
    fireEvent.click(screen.getByRole("button", { name: /mobile/i }));

    await waitFor(() => {
      expect(screen.getByLabelText(/mobile number/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    });

    // Enter credentials and submit
    fireEvent.change(screen.getByLabelText(/mobile number/i), { target: { value: "1234567890" } });
    fireEvent.change(screen.getByLabelText(/password/i), { target: { value: "password123" } });
    fireEvent.click(screen.getByRole("button", { name: /sign in/i }));

    await waitFor(() => {
      expect(actions.loginWithMobilePassword).toHaveBeenCalledWith({ mobile: "1234567890", password: "password123" });
      expect(toast.success).toHaveBeenCalledWith("Sign in successful!", { description: "Redirecting to your dashboard..." });
      expect(mockRouter.push).toHaveBeenCalledWith("/dashboard");
    });
  });

  it("should show an error message for incorrect mobile/password credentials", async () => {
    (actions.loginWithMobilePassword as jest.Mock).mockResolvedValue({ success: false, error: "Invalid credentials" });

    render(<LoginForm />);

    // Switch to mobile/password form
    fireEvent.click(screen.getByRole("button", { name: /mobile/i }));

    await waitFor(() => {
      expect(screen.getByLabelText(/mobile number/i)).toBeInTheDocument();
    });

    // Enter credentials and submit
    fireEvent.change(screen.getByLabelText(/mobile number/i), { target: { value: "1234567890" } });
    fireEvent.change(screen.getByLabelText(/password/i), { target: { value: "wrongpassword" } });
    fireEvent.click(screen.getByRole("button", { name: /sign in/i }));

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith("Login failed", { description: "Invalid credentials" });
      expect(mockRouter.push).not.toHaveBeenCalled();
    });
  });

  it("should handle resend OTP functionality", async () => {
    (actions.sendOTP as jest.Mock).mockResolvedValue({ success: true, message: "OTP sent" });
    render(<LoginForm />);

    // Initial OTP request
    fireEvent.change(screen.getByLabelText(/email/i), { target: { value: "<EMAIL>" } });
    fireEvent.click(screen.getByRole("button", { name: /send otp/i }));

    await waitFor(() => expect(screen.getByRole("button", { name: /resend otp/i })).toBeInTheDocument());

    // Mock countdown to be over
    // In a real scenario, we'd use fake timers
    const resendButton = screen.getByRole("button", { name: /resend otp/i });
    expect(resendButton).toBeEnabled();

    (actions.sendOTP as jest.Mock).mockClear();
    (toast.success as jest.Mock).mockClear();
    (actions.sendOTP as jest.Mock).mockResolvedValue({ success: true, message: "OTP resent" });

    fireEvent.click(resendButton);

    await waitFor(() => {
      expect(actions.sendOTP).toHaveBeenCalledWith({ email: "<EMAIL>" });
      expect(toast.success).toHaveBeenCalledWith("OTP resent!", { description: "OTP resent" });
    });
  });

  it("should handle social login button click", async () => {
    // For social login, we don't mock an action, but rather expect a redirect.
    // The actual social login is handled by Supabase, which is outside the scope of this component test.
    // We just need to ensure the button is there and clickable.
    render(<LoginForm />);

    const socialLoginButton = screen.getByRole("button", { name: /google/i });
    expect(socialLoginButton).toBeInTheDocument();
    expect(socialLoginButton).not.toBeDisabled();

    // We can't test the actual window.location change here, but we can ensure the button is rendered correctly.
    // In a full E2E test, we would verify the redirect.
  });
});