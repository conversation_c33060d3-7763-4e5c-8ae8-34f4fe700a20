"use server";

import { createCustomerProfile } from "@/app/(auth)/choose-role/actions";
import { createClient } from "@/utils/supabase/server";

jest.mock("@/utils/supabase/server");
jest.mock("next/navigation", () => ({
  redirect: jest.fn(),
}));
jest.mock("next/cache", () => ({
  revalidatePath: jest.fn(),
}));

describe("Choose Role Server Action - Performance Benchmarks", () => {
  let mockSupabase: any;

  beforeEach(() => {
    jest.clearAllMocks();
    mockSupabase = {
      auth: {
        getUser: jest.fn(),
      },
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      maybeSingle: jest.fn(),
      insert: jest.fn(),
    };
    (createClient as jest.Mock).mockReturnValue(mockSupabase);
  });

  it("should execute within an acceptable time frame", async () => {
    const MAX_EXECUTION_TIME_MS = 200; // Set a reasonable threshold

    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: "perf-user", email: "<EMAIL>" } },
      error: null,
    });
    mockSupabase.maybeSingle.mockResolvedValue({ data: null, error: null });
    mockSupabase.insert.mockResolvedValue({ error: null });

    const startTime = performance.now();
    await createCustomerProfile("perf-user");
    const endTime = performance.now();

    const executionTime = endTime - startTime;
    console.log(`Server action execution time: ${executionTime.toFixed(2)}ms`);

    expect(executionTime).toBeLessThan(MAX_EXECUTION_TIME_MS);
  });

  it("should execute quickly for existing users", async () => {
    const MAX_EXECUTION_TIME_MS = 50; // Should be much faster

    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: "existing-perf-user", email: "<EMAIL>" } },
      error: null,
    });
    mockSupabase.maybeSingle.mockResolvedValue({
      data: { id: "existing-perf-user" },
      error: null,
    });

    const startTime = performance.now();
    await createCustomerProfile("existing-perf-user");
    const endTime = performance.now();

    const executionTime = endTime - startTime;
    console.log(
      `Server action execution time (existing user): ${executionTime.toFixed(
        2
      )}ms`
    );

    expect(executionTime).toBeLessThan(MAX_EXECUTION_TIME_MS);
  });
});