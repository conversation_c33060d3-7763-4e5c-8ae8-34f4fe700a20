"use server";

import { createCustomerProfile } from "@/app/(auth)/choose-role/actions";
import { createClient } from "@/utils/supabase/server";

jest.mock("@/utils/supabase/server");
jest.mock("next/navigation", () => ({
  redirect: jest.fn().mockImplementation((url) => {
    throw new Error(`REDIRECT: ${url}`);
  }),
}));
jest.mock("next/cache", () => ({
  revalidatePath: jest.fn(),
}));

describe("Choose Role Server Action - Performance Benchmarks", () => {
  let mockSupabase: any;

  beforeEach(() => {
    jest.clearAllMocks();

    // Create a proper query builder mock
    const queryBuilder = {
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      maybeSingle: jest.fn(),
      insert: jest.fn(),
    };

    mockSupabase = {
      auth: {
        getUser: jest.fn(),
      },
      from: jest.fn(() => queryBuilder),
    };
    (createClient as jest.Mock).mockReturnValue(mockSupabase);
  });

  it("should execute within an acceptable time frame", async () => {
    const MAX_EXECUTION_TIME_MS = 200; // Set a reasonable threshold

    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: "perf-user", email: "<EMAIL>" } },
      error: null,
    });

    const queryBuilder = mockSupabase.from();
    queryBuilder.maybeSingle.mockResolvedValue({ data: null, error: null });
    queryBuilder.insert.mockResolvedValue({ error: null });

    const startTime = performance.now();
    try {
      await createCustomerProfile("perf-user");
    } catch (error: any) {
      // Expect redirect error
      expect(error.message).toContain("REDIRECT:");
    }
    const endTime = performance.now();

    const executionTime = endTime - startTime;
    console.log(`Server action execution time: ${executionTime.toFixed(2)}ms`);

    expect(executionTime).toBeLessThan(MAX_EXECUTION_TIME_MS);
  });

  it("should execute quickly for existing users", async () => {
    const MAX_EXECUTION_TIME_MS = 50; // Should be much faster

    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: "existing-perf-user", email: "<EMAIL>" } },
      error: null,
    });

    const queryBuilder = mockSupabase.from();
    queryBuilder.maybeSingle.mockResolvedValue({
      data: { id: "existing-perf-user" },
      error: null,
    });

    const startTime = performance.now();
    try {
      await createCustomerProfile("existing-perf-user");
    } catch (error: any) {
      // Expect redirect error
      expect(error.message).toContain("REDIRECT:");
    }
    const endTime = performance.now();

    const executionTime = endTime - startTime;
    console.log(
      `Server action execution time (existing user): ${executionTime.toFixed(
        2
      )}ms`
    );

    expect(executionTime).toBeLessThan(MAX_EXECUTION_TIME_MS);
  });
});