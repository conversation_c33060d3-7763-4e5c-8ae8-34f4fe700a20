import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { NextIntlClientProvider } from 'next-intl';
import { useRouter } from 'next/router';
import { middleware } from '@/middleware';
import { NextRequest, NextResponse } from 'next/server';
import LoginPage, { generateMetadata } from '@/app/(main)/login/page';
import { ThemeProvider } from 'next-themes';
import { getPostLoginRedirectPath } from '@/lib/actions/redirectAfterLogin';
import { Toaster } from '@/components/ui/sonner';

// Mock Next.js modules
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

// Mock server actions
jest.mock('@/app/(main)/login/actions', () => ({
  sendOTP: jest.fn(),
  verifyOTP: jest.fn(),
  loginWithMobilePassword: jest.fn(),
}));

jest.mock('@/lib/actions/redirectAfterLogin', () => ({
  getPostLoginRedirectPath: jest.fn(),
}));

// Mock Supabase
jest.mock('@/utils/supabase/client', () => ({
  createClient: jest.fn().mockReturnValue({
    auth: {
      getUser: jest.fn().mockResolvedValue({ data: { user: { id: '123' } } }),
    },
  }),
}));

const mockUseRouter = useRouter as jest.Mock;

describe('Next.js Framework-Specific Tests for Login Page', () => {
  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
    mockUseRouter.mockReturnValue({
      push: jest.fn(),
      query: {},
    });
  });

  // Test for Server-Side Rendering (SSR) or Static Site Generation (SSG)
  describe('Metadata Generation (SSG/SSR)', () => {
    it('should generate correct metadata for the login page', async () => {
      const metadata = await generateMetadata();
      expect(metadata.title).toBe('Sign In');
      expect(metadata.description).toContain('Sign in to your Dukancard account or create a new account with just your email address.');
      expect(metadata.robots).toBe('noindex, follow');
    });
  });

  // Test for Next.js API routes and middleware
  describe('API Routes and Middleware', () => {
    it('should redirect authenticated users from the login page', async () => {
      const url = new URL('/login', 'http://localhost:3000');
      const request = new NextRequest(url);
      request.headers.set('x-test-auth-state', 'authenticated');
      request.headers.set('x-test-user-type', 'customer');
      request.headers.set('x-test-has-profile', 'true');

      const response = await middleware(request);

      expect(response.status).toBe(307); // Temporary Redirect
      expect(response.headers.get('location')).toBe('http://localhost:3000/dashboard/customer');
    });

    it.todo('should apply rate limiting to login attempts');
  });

  // Test for page transitions and routing
  describe('Routing and Page Transitions', () => {
    it('should redirect to the correct page after successful login', async () => {
      // Mock a successful login
      const { loginWithMobilePassword } = require('@/app/(main)/login/actions');
      loginWithMobilePassword.mockResolvedValue({ success: true });

      render(
        <ThemeProvider>
          <NextIntlClientProvider locale="en" messages={{}}>
            <LoginPage />
            <Toaster />
          </NextIntlClientProvider>
        </ThemeProvider>
      );

      // Switch to mobile+password form
      fireEvent.click(screen.getByRole('button', { name: /mobile \+ password/i }));

      // Mock the redirect path
      (getPostLoginRedirectPath as jest.Mock).mockResolvedValue('/dashboard/business');

      // Simulate form submission
      fireEvent.change(screen.getByPlaceholderText('+91 98765 43210'), { target: { value: '**********' } });
      fireEvent.change(screen.getByPlaceholderText('••••••••'), { target: { value: 'password123' } });
      fireEvent.click(screen.getByRole('button', { name: /sign in/i }));

      await waitFor(() => {
        expect(useRouter().push).toHaveBeenCalledWith('/dashboard/business');
      });
    });

    it('should handle redirect parameter in the URL', () => {
      mockUseRouter.mockReturnValue({
        push: jest.fn(),
        query: { redirect: 'pricing' },
      });

      render(
        <ThemeProvider>
          <NextIntlClientProvider locale="en" messages={{}}>
            <LoginPage />
          </NextIntlClientProvider>
        </ThemeProvider>
      );

      // Add assertions here to check if the redirect is handled correctly
    });
  });

  // Test for responsive design
  describe('Responsive Design', () => {
    it.todo('should render correctly on mobile devices');
    it.todo('should render correctly on tablet devices');
    it.todo('should render correctly on desktop devices');
  });

  // Test for framework-specific features and optimizations
  describe('Next.js Features and Optimizations', () => {
    it('should display a loading fallback and then the form', async () => {
      // Mock the LoginForm to simulate a delay
      jest.mock('@/app/(main)/login/LoginForm', () => ({
        LoginForm: () => {
          // Simulate a delay
          return new Promise(resolve => setTimeout(() => resolve(<div>Login Form Loaded</div>), 100));
        }
      }));

      render(
        <ThemeProvider>
          <NextIntlClientProvider locale="en" messages={{}}>
            <LoginPage />
          </NextIntlClientProvider>
        </ThemeProvider>
      );

      // Check for the fallback UI first
      expect(screen.getByText('Loading sign in form...')).toBeInTheDocument();

      // Wait for the actual form to be rendered
      await waitFor(() => {
        expect(screen.getByText('Login Form Loaded')).toBeInTheDocument();
      });
    });
  });
});