import React from "react";
import { render, fireEvent, waitFor, screen } from "@testing-library/react";
import { useRouter, useSearchParams } from "next/navigation";
import ChooseRoleClient from "@/app/(auth)/choose-role/ChooseRoleClient";

jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}));

jest.mock("sonner", () => ({
  toast: {
    error: jest.fn(),
  },
}));

jest.mock("@/app/(auth)/choose-role/actions", () => ({
  createCustomerProfile: jest.fn().mockResolvedValue({ success: true }),
}));

describe("ChooseRoleClient Routing and Page Transitions", () => {
  let mockRouter: any;
  let mockSearchParams: URLSearchParams;

  beforeEach(() => {
    mockRouter = {
      push: jest.fn(),
    };
    (useRouter as jest.Mock).mockReturnValue(mockRouter);

    mockSearchParams = new URLSearchParams();
    (useSearchParams as jest.Mock).mockReturnValue(mockSearchParams);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should push to the onboarding page with redirect and message params when business role is selected", async () => {
    mockSearchParams.set("redirect", "/special-dashboard");
    mockSearchParams.set("message", "Welcome back");

    render(
      <ChooseRoleClient
        userId="test-user"
        redirectSlug={null}
        message={null}
      />
    );

    fireEvent.click(screen.getByText("As a Business"));

    await waitFor(() => {
      expect(mockRouter.push).toHaveBeenCalledWith(
        "/onboarding?redirect=%2Fspecial-dashboard&message=Welcome+back"
      );
    });
  });

  it("should prioritize props over URL search params for redirection", async () => {
    mockSearchParams.set("redirect", "/url-param-dashboard");
    render(
      <ChooseRoleClient
        userId="test-user"
        redirectSlug="/prop-dashboard"
        message="From Prop"
      />
    );

    fireEvent.click(screen.getByText("As a Business"));

    await waitFor(() => {
      expect(mockRouter.push).toHaveBeenCalledWith(
        "/onboarding?redirect=%2Fprop-dashboard&message=From+Prop"
      );
    });
  });

  it("should handle missing redirect and message params gracefully", async () => {
    render(<ChooseRoleClient userId="test-user" />);

    fireEvent.click(screen.getByText("As a Business"));

    await waitFor(() => {
      expect(mockRouter.push).toHaveBeenCalledWith("/onboarding");
    });
  });

  it("should read from localStorage as a fallback for redirect and message params", async () => {
    Storage.prototype.getItem = jest.fn((key) => {
      if (key === "chooseRoleRedirect") return "/local-storage-dashboard";
      if (key === "chooseRoleMessage") return "From Local Storage";
      return null;
    });

    render(<ChooseRoleClient userId="test-user" />);

    fireEvent.click(screen.getByText("As a Business"));

    await waitFor(() => {
      expect(mockRouter.push).toHaveBeenCalledWith(
        "/onboarding?redirect=%2Flocal-storage-dashboard&message=From+Local+Storage"
      );
      expect(localStorage.getItem).toHaveBeenCalledWith("chooseRoleRedirect");
      expect(localStorage.getItem).toHaveBeenCalledWith("chooseRoleMessage");
    });
  });
});