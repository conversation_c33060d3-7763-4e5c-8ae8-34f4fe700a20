import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { toast } from 'sonner';
import ChooseRoleClient from '@/app/(auth)/choose-role/ChooseRoleClient';
import { createCustomerProfile } from '@/app/(auth)/choose-role/actions';

// Mock dependencies
jest.mock('sonner');
jest.mock('@/app/(auth)/choose-role/actions');

const mockCreateCustomerProfile = createCustomerProfile as jest.Mock;
const mockToast = toast as jest.Mocked<typeof toast>;

// Mock next/navigation hooks
const mockPush = jest.fn();
const mockGet = jest.fn();

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
  useSearchParams: () => ({
    get: mockGet,
  }),
}));

describe('ChooseRoleClient', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Default mock behavior for URL parameters
    mockGet.mockImplementation((param: string) => {
      if (param === 'redirect') return '/dashboard';
      if (param === 'message') return 'Welcome!';
      return null;
    });
  });

  it('renders the component with the correct title and description', () => {
    render(<ChooseRoleClient userId="test-user-id" />);
    expect(screen.getByText('Choose Your Role')).toBeInTheDocument();
    expect(screen.getByText(/Select how you'll be using our platform/)).toBeInTheDocument();
  });

  it('handles the customer role selection successfully', async () => {
    mockCreateCustomerProfile.mockResolvedValue({ success: true });
    render(<ChooseRoleClient userId="test-user-id" />);

    const customerButton = screen.getByRole('button', { name: /as a customer/i });
    fireEvent.click(customerButton);

    await waitFor(() => {
      expect(mockCreateCustomerProfile).toHaveBeenCalledWith('test-user-id', '/dashboard', 'Welcome!');
    });
  });

  it('shows an error toast if customer profile creation fails', async () => {
    mockCreateCustomerProfile.mockResolvedValue({ error: 'Something went wrong' });
    render(<ChooseRoleClient userId="test-user-id" />);

    const customerButton = screen.getByRole('button', { name: /as a customer/i });
    fireEvent.click(customerButton);

    await waitFor(() => {
      expect(mockToast.error).toHaveBeenCalledWith('Failed to set up account: Something went wrong');
    });
  });

  it('handles the business role selection and redirects', () => {
    render(<ChooseRoleClient userId="test-user-id" />);
    const businessButton = screen.getByRole('button', { name: /as a business/i });
    fireEvent.click(businessButton);
    expect(mockPush).toHaveBeenCalledWith('/onboarding?redirect=%2Fdashboard&message=Welcome%21');
  });

  it('passes redirect and message params to the business onboarding page', () => {
    mockGet.mockImplementation((key: string) => {
      if (key === 'redirect') return '/dashboard';
      if (key === 'message') return 'Welcome!';
      return null;
    });

    render(<ChooseRoleClient userId="test-user-id" />);
    const businessButton = screen.getByRole('button', { name: /as a business/i });
    fireEvent.click(businessButton);
    expect(mockPush).toHaveBeenCalledWith('/onboarding?redirect=%2Fdashboard&message=Welcome%21');
  });

  it('shows a loading spinner when a role is being selected', async () => {
    // Use a promise that doesn't resolve immediately
    mockCreateCustomerProfile.mockReturnValue(new Promise(() => {}));
    render(<ChooseRoleClient userId="test-user-id" />);

    const customerButton = screen.getByRole('button', { name: /as a customer/i });
    fireEvent.click(customerButton);

    await waitFor(() => {
        const loaders = screen.getAllByText((_content, element) => {
            return element?.tagName.toLowerCase() === 'svg' && /animate-spin/.test(element.getAttribute('class') || '');
        });
        expect(loaders.length).toBeGreaterThan(0);
    });
  });

  it('gets redirect and message from localStorage if not in props or URL', () => {
    // Mock localStorage
    Storage.prototype.getItem = jest.fn((key: string) => {
        if (key === 'chooseRoleRedirect') return '/special-redirect';
        if (key === 'chooseRoleMessage') return 'Special Message';
        return null;
    });
    Storage.prototype.removeItem = jest.fn();

    // Ensure URL parameters return null so localStorage is checked
    mockGet.mockReturnValue(null);

    render(<ChooseRoleClient userId="test-user-id" />);
    const businessButton = screen.getByRole('button', { name: /as a business/i });
    fireEvent.click(businessButton);

    expect(mockPush).toHaveBeenCalledWith('/onboarding?redirect=%2Fspecial-redirect&message=Special+Message');
  });
});