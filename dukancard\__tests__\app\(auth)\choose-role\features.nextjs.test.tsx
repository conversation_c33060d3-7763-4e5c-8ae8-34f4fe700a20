import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import ChooseRolePage, {
  generateMetadata,
} from "@/app/(auth)/choose-role/page";
import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";

jest.mock("@/utils/supabase/server");
jest.mock("next/navigation", () => ({
  redirect: jest.fn(),
}));
jest.mock("@/app/(auth)/choose-role/ChooseRoleClient", () => {
  return {
    __esModule: true,
    default: ({ userId }: { userId: string }) => (
      <div data-testid="choose-role-client">User ID: {userId}</div>
    ),
  };
});

describe("ChooseRolePage - Next.js Features", () => {
  let mockSupabase: any;

  beforeEach(() => {
    jest.clearAllMocks();
    mockSupabase = {
      auth: {
        getUser: jest.fn(),
      },
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
    };
    (createClient as jest.Mock).mockReturnValue(mockSupabase);
  });

  describe("generateMetadata", () => {
    it("should generate correct metadata", async () => {
      const metadata = await generateMetadata();
      expect(metadata.title).toBe("Choose Your Role");
      expect(metadata.description).toBe("Select how you will use Dukancard.");
      expect(metadata.robots).toBe("noindex, nofollow");
    });
  });

  describe("Server-Side Rendering", () => {
    it("should render the ChooseRoleClient when user is authenticated and has no profile", async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: "test-user" } },
        error: null,
      });

      const searchParams = Promise.resolve({});
      render(await ChooseRolePage({ searchParams }));

      await waitFor(() => {
        const client = screen.getByTestId("choose-role-client");
        expect(client).toBeInTheDocument();
        expect(client).toHaveTextContent("User ID: test-user");
      });
    });

    it("should redirect to /login if user is not authenticated", async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null,
      });

      const searchParams = Promise.resolve({});
      await ChooseRolePage({ searchParams });

      expect(redirect).toHaveBeenCalledWith("/login");
    });

    it("should redirect to the correct dashboard if a customer profile exists", async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: "customer-user" } },
        error: null,
      });
      mockSupabase.from.mockImplementation((table: string) => {
        if (table === "customer_profiles") {
          return {
            select: jest.fn().mockReturnThis(),
            eq: jest.fn().mockReturnThis(),
            maybeSingle: jest
              .fn()
              .mockResolvedValue({ data: { id: "customer-user" }, error: null }),
          };
        }
        return {
          select: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
        };
      });

      const searchParams = Promise.resolve({});
      await ChooseRolePage({ searchParams });

      expect(redirect).toHaveBeenCalledWith("/dashboard/customer");
    });
  });
});