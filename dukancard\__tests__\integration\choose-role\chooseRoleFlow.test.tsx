import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import ChooseRoleClient from "@/app/(auth)/choose-role/ChooseRoleClient";
import * as chooseRoleActions from "@/app/(auth)/choose-role/actions";
import { createClient } from "@/utils/supabase/server";
import { updateSession } from "@/utils/supabase/middleware";
import MinimalHeader from "@/app/components/MinimalHeader";
import React from "react";

// Mocks
jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(() => new URLSearchParams()),
  usePathname: jest.fn().mockReturnValue("/choose-role"),
}));
jest.mock("sonner");
jest.mock("@/app/(auth)/choose-role/actions", () => ({
  createCustomerProfile: jest.fn(),
}));
jest.mock("@/utils/supabase/server");
jest.mock("@/app/auth/actions", () => ({
  signOutUser: jest.fn(),
}));

const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  prefetch: jest.fn(),
  back: jest.fn(),
  forward: jest.fn(),
};
const mockUseRouter = useRouter as jest.Mock;
mockUseRouter.mockReturnValue(mockRouter);

const mockCreateCustomerProfile =
  chooseRoleActions.createCustomerProfile as jest.Mock;

describe("Choose Role Page Integration Flow", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock localStorage
    Storage.prototype.getItem = jest.fn();
    Storage.prototype.removeItem = jest.fn();
  });

  describe("Component Interactions & Form Submission", () => {
    it("should render the ChooseRoleClient component and allow customer role selection and successful redirection", async () => {
      mockCreateCustomerProfile.mockResolvedValue({ success: true });

      render(<ChooseRoleClient userId="test-user-id" />);

      expect(screen.getByText("Choose Your Role")).toBeInTheDocument();
      const customerButton = screen.getByText("As a Customer");
      fireEvent.click(customerButton);

      await waitFor(() => {
        expect(mockCreateCustomerProfile).toHaveBeenCalledWith(
          "test-user-id",
          null,
          null
        );
      });
    });

    it("should handle business role selection and redirect to onboarding", async () => {
      render(<ChooseRoleClient userId="test-user-id" />);

      const businessButton = screen.getByText("As a Business");
      fireEvent.click(businessButton);

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith("/onboarding");
      });
    });

    it("should show an error toast if creating a customer profile fails", async () => {
      const errorMessage = "Failed to create profile.";
      mockCreateCustomerProfile.mockResolvedValue({ error: errorMessage });

      render(<ChooseRoleClient userId="test-user-id" />);

      const customerButton = screen.getByText("As a Customer");
      fireEvent.click(customerButton);

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith(
          `Failed to set up account: ${errorMessage}`
        );
      });
    });
  });

  describe("Navigation and Routing", () => {
    it("should redirect to /login if user is not authenticated (middleware)", async () => {
      const request = {
        nextUrl: new URL("http://localhost/choose-role"),
        cookies: {
          getAll: () => [],
          setAll: () => {},
          get: () => undefined, // Ensure no cookies are found
        },
        headers: new Headers(),
      } as any;

      const supabase = {
        auth: {
          getUser: () => Promise.resolve({ data: { user: null }, error: null }),
        },
      };

      (createClient as jest.Mock).mockReturnValue(supabase);

      const response = await updateSession(request);
      expect(response.status).toBe(307);
      expect(response.headers.get("location")).toBe(
        "http://localhost/login?next=%2Fchoose-role"
      );
    });

    it("should redirect to customer dashboard if a customer profile already exists (middleware)", async () => {
      const request = {
        nextUrl: new URL("http://localhost/choose-role"),
        cookies: {
          getAll: () => [],
          setAll: () => {},
          get: () => ({ name: 'sb-token', value: 'test-token' }),
        },
        headers: new Headers(),
      } as any;

      const supabase = {
        auth: {
          getUser: () =>
            Promise.resolve({ data: { user: { id: "test-user" } }, error: null }),
        },
        from: jest.fn().mockImplementation((table: string) => {
          if (table === "customer_profiles") {
            return {
              select: jest.fn().mockReturnThis(),
              eq: jest.fn().mockReturnThis(),
              maybeSingle: () =>
                Promise.resolve({ data: { id: "test-user" }, error: null }),
            };
          }
          return {
            select: jest.fn().mockReturnThis(),
            eq: jest.fn().mockReturnThis(),
            maybeSingle: () => Promise.resolve({ data: null, error: null }),
          };
        }),
      };

      (createClient as jest.Mock).mockReturnValue(supabase);

      const response = await updateSession(request);
      expect(response.status).toBe(307);
      expect(response.headers.get("location")).toBe(
        "http://localhost/dashboard/customer"
      );
    });
  });

  describe("Data Flow and State Management", () => {
    it("should use redirectSlug and message from props", async () => {
      render(
        <ChooseRoleClient
          userId="test-user-id"
          redirectSlug="/prop-redirect"
          message="Prop Message"
        />
      );

      const customerButton = screen.getByText("As a Customer");
      fireEvent.click(customerButton);

      await waitFor(() => {
        expect(mockCreateCustomerProfile).toHaveBeenCalledWith(
          "test-user-id",
          "/prop-redirect",
          "Prop Message"
        );
      });

      const businessButton = screen.getByText("As a Business");
      fireEvent.click(businessButton);

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith(
          "/onboarding?redirect=%2Fprop-redirect&message=Prop%20Message"
        );
      });
    });

    it("should fall back to localStorage if props are not provided", async () => {
      (localStorage.getItem as jest.Mock).mockImplementation((key) => {
        if (key === "chooseRoleRedirect") return "/local-redirect";
        if (key === "chooseRoleMessage") return "Local Message";
        return null;
      });

      render(<ChooseRoleClient userId="test-user-id" />);

      const customerButton = screen.getByText("As a Customer");
      fireEvent.click(customerButton);

      await waitFor(() => {
        expect(mockCreateCustomerProfile).toHaveBeenCalledWith(
          "test-user-id",
          "/local-redirect",
          "Local Message"
        );
      });
    });

    it("should show a loading spinner when a role is being selected", async () => {
      mockCreateCustomerProfile.mockImplementation(
        () => new Promise((resolve) => setTimeout(() => resolve({ success: true }), 100))
      );

      render(<ChooseRoleClient userId="test-user-id" />);

      const customerButton = screen.getByText("As a Customer");
      fireEvent.click(customerButton);

      await waitFor(() => {
        // The loader is identified by its class `animate-spin`
        const loader = customerButton.querySelector('.animate-spin');
        expect(loader).toBeInTheDocument();
      });
    });
  });

  describe("Cross-Component Communication", () => {
    it("should render MinimalHeader with a logout button on the choose-role page", () => {
      // We need to mock usePathname to return the correct path for MinimalHeader
      (require("next/navigation").usePathname as jest.Mock).mockReturnValue('/choose-role');
      
      render(
        <>
          <MinimalHeader />
          <ChooseRoleClient userId="test-user-id" />
        </>
      );

      expect(screen.getByText("Log out")).toBeInTheDocument();
    });
  });
});