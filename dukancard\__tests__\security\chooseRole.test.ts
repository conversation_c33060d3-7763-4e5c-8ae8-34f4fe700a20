import { createCustomerProfile } from "@/app/(auth)/choose-role/actions";
import { createClient } from "@/utils/supabase/server";

// Mocking Supabase and Next.js utilities
jest.mock("@/utils/supabase/server", () => ({
  createClient: jest.fn(),
}));

jest.mock("next/navigation", () => ({
  redirect: jest.fn(),
}));

jest.mock("next/cache", () => ({
  revalidatePath: jest.fn(),
}));

const mockSupabase = createClient as jest.Mock;
const mockRedirect = require("next/navigation").redirect as jest.Mock;

describe("Choose Role - Security Tests", () => {
  
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("createCustomerProfile Server Action", () => {

    it("should return an error if userId is null or empty", async () => {
      // @ts-ignore
      const result = await createCustomerProfile(null);
      expect(result).toEqual({ error: "User ID is required." });

      const result2 = await createCustomerProfile("");
      expect(result2).toEqual({ error: "User ID is required." });
    });

    it("should handle malicious userId input gracefully", async () => {
      const maliciousId = "'; DROP TABLE users; --";
      const mockGetUser = jest.fn().mockResolvedValue({ data: { user: null }, error: { message: "Invalid token", status: 401 } });
      mockSupabase.mockReturnValue({ auth: { getUser: mockGetUser } });

      const result = await createCustomerProfile(maliciousId);
      
      expect(mockGetUser).toHaveBeenCalled();
      expect(result).toEqual({ error: "User not found or authentication error." });
    });

    it("should prevent open redirect via redirectSlug", async () => {
      const userId = "test-user-id";
      const maliciousRedirect = "//evil.com";
      
      const mockUser = { id: userId, user_metadata: { full_name: "Test User" }, email: "<EMAIL>" };
      const mockGetUser = jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null });
      const mockFrom = jest.fn().mockReturnThis();
      const mockSelect = jest.fn().mockReturnThis();
      const mockEq = jest.fn().mockResolvedValue({ data: null, error: null }); // No existing profile

      mockSupabase.mockReturnValue({ 
        auth: { getUser: mockGetUser },
        from: mockFrom,
        select: mockSelect,
        eq: mockEq,
        insert: jest.fn().mockResolvedValue({ error: null })
      });

      // This test relies on the fact that Next.js's `redirect()` function
      // is supposed to handle open redirects by throwing an error if the URL
      // is external. We are testing that our code *calls* redirect, and we
      // trust Next.js to do its job.
      await createCustomerProfile(userId, maliciousRedirect, null);

      // We expect our code to call redirect, but Next.js should prevent it from being an external one.
      // In a real test environment, this would throw an error. Here, we check it was called.
      expect(mockRedirect).toHaveBeenCalledWith(`/${maliciousRedirect}`);
    });

    it("should not allow redirect to a different domain", async () => {
        const userId = "test-user-id";
        const externalRedirect = "http://evil.com";

        const mockUser = { id: userId, user_metadata: { full_name: "Test User" }, email: "<EMAIL>" };
        const mockGetUser = jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null });
        const mockFrom = jest.fn().mockReturnThis();
        const mockSelect = jest.fn().mockReturnThis();
        const mockEq = jest.fn().mockResolvedValue({ data: null, error: null });

        mockSupabase.mockReturnValue({ 
            auth: { getUser: mockGetUser },
            from: mockFrom,
            select: mockSelect,
            eq: mockEq,
            insert: jest.fn().mockResolvedValue({ error: null })
        });
        
        // We expect the call to `createCustomerProfile` to throw an error because
        // Next.js `redirect` will throw an error for external URLs.
        await expect(createCustomerProfile(userId, externalRedirect, null)).rejects.toThrow();
    });


    it("should handle SQL injection attempts in userId gracefully", async () => {
      const sqlInjectionId = "1' OR '1'='1";
      const mockGetUser = jest.fn().mockResolvedValue({ data: { user: null }, error: { message: "Invalid token", status: 401 } });
      mockSupabase.mockReturnValue({ auth: { getUser: mockGetUser } });

      const result = await createCustomerProfile(sqlInjectionId);
      
      expect(mockGetUser).toHaveBeenCalled();
      expect(result).toEqual({ error: "User not found or authentication error." });
    });

    it("should not create a profile if the user session is invalid", async () => {
        const userId = "user-with-invalid-session";
        const mockGetUser = jest.fn().mockResolvedValue({ data: { user: null }, error: { message: "Invalid session", status: 401 } });
        const mockInsert = jest.fn();

        mockSupabase.mockReturnValue({ 
            auth: { getUser: mockGetUser },
            from: jest.fn().mockReturnThis(),
            insert: mockInsert
        });

        const result = await createCustomerProfile(userId);

        expect(mockGetUser).toHaveBeenCalled();
        expect(mockInsert).not.toHaveBeenCalled();
        expect(result).toEqual({ error: "User not found or authentication error." });
    });
  });
});
    describe("Sensitive Data Handling", () => {
      it("should not leak PII in error messages when profile creation fails", async () => {
        const userId = "test-user-id";
        const user = { id: userId, email: "<EMAIL>", user_metadata: { full_name: "Sensitive Name" } };
        
        const mockGetUser = jest.fn().mockResolvedValue({ data: { user }, error: null });
        const mockInsert = jest.fn().mockResolvedValue({ error: { message: "DB insert failed", code: "12345" } });
        const mockFrom = jest.fn().mockReturnThis();
        const mockSelect = jest.fn().mockReturnThis();
        const mockEq = jest.fn().mockResolvedValue({ data: null, error: null });

        mockSupabase.mockReturnValue({
          auth: { getUser: mockGetUser },
          from: mockFrom,
          select: mockSelect,
          eq: mockEq,
          insert: mockInsert,
        });

        const result = await createCustomerProfile(userId);

        expect(result).toBeDefined();
        expect(result.error).toBe("Failed to create profile.");
        expect(result.error).not.toContain(user.email);
        expect(result.error).not.toContain(user.user_metadata.full_name);
      });

      it("should not expose user email or name in error if user fetch fails", async () => {
        const userId = "another-user-id";
        const mockGetUser = jest.fn().mockResolvedValue({ data: { user: null }, error: { message: "Internal server error" } });
        
        mockSupabase.mockReturnValue({
          auth: { getUser: mockGetUser },
        });

        const result = await createCustomerProfile(userId);

        expect(result).toBeDefined();
        expect(result.error).toBe("User not found or authentication error.");
        expect(result.error).not.toContain("email");
        expect(result.error).not.toContain("name");
      });
    });