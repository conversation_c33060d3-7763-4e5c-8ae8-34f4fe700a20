import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MobilePasswordForm } from '@/app/(main)/login/components/MobilePasswordForm';

describe('MobilePasswordForm', () => {
  const onSubmit = jest.fn();

  it('renders the mobile and password fields', () => {
    render(<MobilePasswordForm isPending={false} onSubmit={onSubmit} />);
    expect(screen.getByLabelText(/mobile number/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
  });

  it('calls onSubmit with correct values', async () => {
    render(<MobilePasswordForm isPending={false} onSubmit={onSubmit} />);
    fireEvent.change(screen.getByLabelText(/mobile number/i), { target: { value: '9876543210' } });
    fireEvent.change(screen.getByLabelText(/password/i), { target: { value: 'password' } });
    fireEvent.click(screen.getByRole('button', { name: /sign in/i }));
    await waitFor(() => {
      expect(onSubmit).toHaveBeenCalledWith({ mobile: '9876543210', password: 'password' });
    });
  });

  it('shows validation errors for invalid input', async () => {
    render(<MobilePasswordForm isPending={false} onSubmit={onSubmit} />);

    // Find the mobile input by placeholder or name attribute
    const mobileInput = screen.getByPlaceholderText('Enter your mobile number');
    fireEvent.change(mobileInput, { target: { value: '123' } });

    fireEvent.click(screen.getByRole('button', { name: /sign in/i }));

    await waitFor(() => {
      expect(screen.getByText(/please enter a valid 10-digit mobile number/i)).toBeInTheDocument();
    });
  });
});