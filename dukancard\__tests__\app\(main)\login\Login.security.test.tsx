import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { NextIntlClientProvider } from 'next-intl';
import { ThemeProvider } from 'next-themes';
import LoginPage from '@/app/(main)/login/page';
import { Toaster } from '@/components/ui/sonner';
import * as loginActions from '@/app/(main)/login/actions';

// Mock server actions to inspect payloads
jest.mock('@/app/(main)/login/actions', () => ({
  sendOTP: jest.fn(),
  verifyOTP: jest.fn(),
  loginWithMobilePassword: jest.fn(),
}));

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    query: {},
  }),
}));

describe('Login Page Security Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const renderComponent = () => {
    render(
      <ThemeProvider>
        <NextIntlClientProvider locale="en" messages={{}}>
          <LoginPage />
          <Toaster />
        </NextIntlClientProvider>
      </ThemeProvider>
    );
  };

  describe('Input Sanitization and XSS Prevention', () => {
    it('should sanitize email input to prevent XSS attacks', async () => {
      const xssPayload = '<script>alert("XSS")</script>';
      const sanitizedPayload = ''; // Assuming the input is cleared or sanitized
      (loginActions.sendOTP as jest.Mock).mockResolvedValue({ success: true });

      renderComponent();
      fireEvent.change(screen.getByPlaceholderText('<EMAIL>'), { target: { value: xssPayload } });
      fireEvent.click(screen.getByRole('button', { name: /send otp/i }));

      await waitFor(() => {
        // Expect sendOTP to be called with a sanitized version of the payload
        // The exact sanitization depends on the implementation (e.g., DOMPurify, or simple stripping)
        // For this test, we'll assume it's stripped.
        expect(loginActions.sendOTP).toHaveBeenCalledWith({
          email: sanitizedPayload,
        });
      });
    });

    it('should sanitize mobile number input to prevent malicious injections', async () => {
      const maliciousPayload = '9876543210<script>alert("XSS")</script>';
      const sanitizedPayload = '9876543210';
      (loginActions.loginWithMobilePassword as jest.Mock).mockResolvedValue({ success: true });

      renderComponent();
      fireEvent.click(screen.getByRole('button', { name: /mobile \+ password/i }));
      fireEvent.change(screen.getByPlaceholderText('+91 98765 43210'), { target: { value: maliciousPayload } });
      fireEvent.change(screen.getByPlaceholderText('••••••••'), { target: { value: 'password123' } });
      fireEvent.click(screen.getByRole('button', { name: /sign in/i }));

      await waitFor(() => {
        expect(loginActions.loginWithMobilePassword).toHaveBeenCalledWith(
          expect.objectContaining({
            mobile: sanitizedPayload,
          })
        );
      });
    });
  });

  describe('Sensitive Data Handling', () => {
    it('should not expose sensitive data in error messages', async () => {
      const password = 'wrongpassword';
      (loginActions.loginWithMobilePassword as jest.Mock).mockResolvedValue({
        success: false,
        error: 'Invalid login credentials',
      });

      renderComponent();
      fireEvent.click(screen.getByRole('button', { name: /mobile \+ password/i }));
      fireEvent.change(screen.getByPlaceholderText('+91 98765 43210'), { target: { value: '9876543210' } });
      fireEvent.change(screen.getByPlaceholderText('••••••••'), { target: { value: password } });
      fireEvent.click(screen.getByRole('button', { name: /sign in/i }));

      await waitFor(() => {
        const errorMessage = screen.getByText('Invalid login credentials');
        expect(errorMessage).toBeInTheDocument();
        expect(errorMessage.textContent).not.toContain(password);
      });
    });

    it('should not log passwords or other sensitive information', () => {
      const consoleSpy = jest.spyOn(console, 'log');
      renderComponent();
      fireEvent.click(screen.getByRole('button', { name: /mobile \+ password/i }));
      fireEvent.change(screen.getByPlaceholderText('••••••••'), { target: { value: 'any-password' } });
      
      expect(consoleSpy).not.toHaveBeenCalledWith(expect.stringContaining('any-password'));
      consoleSpy.mockRestore();
    });
  });

  describe('SQL Injection Prevention', () => {
    it('should prevent SQL injection in the email field', async () => {
      const sqlPayload = "' OR '1'='1";
      (loginActions.sendOTP as jest.Mock).mockResolvedValue({ success: false, error: 'Invalid email address' });

      renderComponent();
      fireEvent.change(screen.getByPlaceholderText('<EMAIL>'), { target: { value: sqlPayload } });
      fireEvent.click(screen.getByRole('button', { name: /send otp/i }));

      await waitFor(() => {
        // The action should be called with the payload, but the backend should reject it.
        // The frontend should display a generic error.
        expect(loginActions.sendOTP).toHaveBeenCalled();
        expect(screen.getByText('Invalid email address')).toBeInTheDocument();
      });
    });

    it('should prevent SQL injection in the password field', async () => {
      const sqlPayload = "' OR '1'='1";
      (loginActions.loginWithMobilePassword as jest.Mock).mockResolvedValue({
        success: false,
        error: 'Invalid login credentials',
      });

      renderComponent();
      fireEvent.click(screen.getByRole('button', { name: /mobile \+ password/i }));
      fireEvent.change(screen.getByPlaceholderText('+91 98765 43210'), { target: { value: '9876543210' } });
      fireEvent.change(screen.getByPlaceholderText('••••••••'), { target: { value: sqlPayload } });
      fireEvent.click(screen.getByRole('button', { name: /sign in/i }));

      await waitFor(() => {
        expect(loginActions.loginWithMobilePassword).toHaveBeenCalled();
        expect(screen.getByText('Invalid login credentials')).toBeInTheDocument();
      });
    });
  });

  describe('CSRF Protection', () => {
    it.todo('should include a CSRF token in the form submission');
  });
});