import { addProductService } from '@/app/(dashboard)/dashboard/business/products/actions/addProduct';
import { createClient } from '@/utils/supabase/server';
import { revalidatePath } from 'next/cache';

// Mock dependencies
jest.mock('@/utils/supabase/server', () => ({
  createClient: jest.fn(),
}));
jest.mock('next/cache', () => ({
  revalidatePath: jest.fn(),
}));

const mockSupabase = createClient as jest.Mock;

describe('addProductService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return an error if the user is not authenticated', async () => {
    // Arrange
    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ error: new Error('Auth error'), data: { user: null } }),
      },
    });
    const formData = new FormData();

    // Act
    const result = await addProductService(formData);

    // Assert
    expect(result.success).toBe(false);
    expect(result.error).toBe('User not authenticated.');
  });

  it('should return a validation error for invalid data', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
    });
    const formData = new FormData();
    formData.append('name', ''); // Invalid name

    // Act
    const result = await addProductService(formData);

    // Assert
    expect(result.success).toBe(false);
    expect(result.error).toContain('Invalid data');
  });

  it('should add a product successfully', async () => {
    // Arrange
    const mockUser = { id: 'user-123' };
    const mockProduct = { id: 'prod-123', name: 'Test Product' };
    const mockInsert = jest.fn().mockReturnThis();
    const mockSelect = jest.fn().mockReturnThis();
    const mockSingle = jest.fn().mockResolvedValue({ data: mockProduct, error: null });

    mockSupabase.mockReturnValue({
      auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
      },
      from: jest.fn(() => ({
        insert: mockInsert,
        select: mockSelect,
        single: mockSingle,
      })),
    });

    const formData = new FormData();
    formData.append('name', 'Test Product');
    formData.append('base_price', '100');
    formData.append('product_type', 'physical');

    // Act
    const result = await addProductService(formData);

    // Assert
    expect(result.success).toBe(true);
    expect(result.data?.id).toBe('prod-123');
    expect(revalidatePath).toHaveBeenCalledWith('/dashboard/business/products');
  });
});