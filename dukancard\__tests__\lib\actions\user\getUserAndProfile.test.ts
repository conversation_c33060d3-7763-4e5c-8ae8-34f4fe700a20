import { getUserAndProfile } from "@/lib/actions/user/getUserAndProfile";
import { describe, it, expect, jest, beforeEach } from "@jest/globals";

// Mock the subscription utils module
const mockGetUserAndProfile = jest.fn();

jest.mock("@/lib/actions/subscription/utils", () => ({
  getUserAndProfile: mockGetUserAndProfile,
}));

describe("getUserAndProfile", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should return an error if user is not authenticated", async () => {
    mockGetUserAndProfile.mockResolvedValue({
      error: "User not authenticated",
      user: null,
      profile: null,
      subscription: null,
    });

    const result = await getUserAndProfile();
    expect(result.error).toBe("User not authenticated");
    expect(result.user).toBeNull();
    expect(result.profile).toBeNull();
    expect(result.subscription).toBeNull();
  });

  it("should return an error if business profile is not found", async () => {
    const user = { id: "test-user-id" };
    mockGetUserAndProfile.mockResolvedValue({
      error: "Business profile not found. Please complete onboarding first.",
      user,
      profile: null,
      subscription: null,
    });

    const result = await getUserAndProfile();
    expect(result.error).toBe(
      "Business profile not found. Please complete onboarding first."
    );
    expect(result.user).toEqual(user);
    expect(result.profile).toBeNull();
    expect(result.subscription).toBeNull();
  });

  it("should return user, profile, and subscription on success", async () => {
    const user = { id: "test-user-id" };
    const profile = { id: "test-user-id", business_name: "Test Business" };
    const subscription = { id: "sub-id", plan: "premium" };

    mockGetUserAndProfile.mockResolvedValue({
      error: null,
      user,
      profile,
      subscription,
    });

    const result = await getUserAndProfile();

    expect(result.error).toBeNull();
    expect(result.user).toEqual(user);
    expect(result.profile).toEqual(profile);
    expect(result.subscription).toEqual(subscription);
  });

  it("should handle the case where there is no subscription", async () => {
    const user = { id: "test-user-id" };
    const profile = { id: "test-user-id", business_name: "Test Business" };

    mockGetUserAndProfile.mockResolvedValue({
      error: null,
      user,
      profile,
      subscription: null,
    });

    const result = await getUserAndProfile();

    expect(result.error).toBeNull();
    expect(result.user).toEqual(user);
    expect(result.profile).toEqual(profile);
    expect(result.subscription).toBeNull();
  });
});