import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { NextIntlClientProvider } from 'next-intl';
import { ThemeProvider } from 'next-themes';
import LoginPage from '@/app/(main)/login/page';
import { Toaster } from '@/components/ui/sonner';
import * as loginActions from '@/app/(main)/login/actions';
import { performance } from 'perf_hooks';

// Mock server actions
jest.mock('@/app/(main)/login/actions', () => ({
  sendOTP: jest.fn(),
  verifyOTP: jest.fn(),
  loginWithMobilePassword: jest.fn(),
}));

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    query: {},
  }),
}));

describe('Login Page Performance Tests', () => {
  const renderComponent = () => {
    render(
      <ThemeProvider>
        <NextIntlClientProvider locale="en" messages={{}}>
          <LoginPage />
          <Toaster />
        </NextIntlClientProvider>
      </ThemeProvider>
    );
  };

  describe('Page Load and Render Times', () => {
    it('should render the login page within an acceptable time frame', () => {
      const startTime = performance.now();
      renderComponent();
      const endTime = performance.now();
      const renderTime = endTime - startTime;

      console.log(`Login page render time: ${renderTime.toFixed(2)}ms`);
      expect(renderTime).toBeLessThan(1000); // Example threshold: 1 second
    });

    it('should switch between login forms without significant delay', async () => {
      renderComponent();
      const startTime = performance.now();
      fireEvent.click(screen.getByRole('button', { name: /mobile \+ password/i }));
      await screen.findByPlaceholderText('+91 98765 43210');
      const endTime = performance.now();
      const switchTime = endTime - startTime;

      console.log(`Form switch time: ${switchTime.toFixed(2)}ms`);
      expect(switchTime).toBeLessThan(500); // Example threshold: 500ms
    });
  });

  describe('Responsiveness Under Load', () => {
    it('should handle multiple login attempts without crashing', async () => {
      (loginActions.loginWithMobilePassword as jest.Mock).mockResolvedValue({
        success: false,
        error: 'Invalid login credentials',
      });
      renderComponent();
      fireEvent.click(screen.getByRole('button', { name: /mobile \+ password/i }));

      const attempts = Array.from({ length: 10 }, (_, i) => i);
      for (const attempt of attempts) {
        fireEvent.change(screen.getByPlaceholderText('+91 98765 43210'), { target: { value: `987654321${attempt}` } });
        fireEvent.change(screen.getByPlaceholderText('••••••••'), { target: { value: 'password123' } });
        fireEvent.click(screen.getByRole('button', { name: /sign in/i }));
      }

      await waitFor(() => {
        expect(loginActions.loginWithMobilePassword).toHaveBeenCalledTimes(10);
      });
    });
  });

  describe('Memory Leak Detection', () => {
    it.todo('should not have memory leaks after repeated rendering and un-rendering');
    // This typically requires more advanced tooling and a controlled environment to test effectively.
    // Example with a library like 'node-memwatch' or by analyzing heap snapshots.
  });

  describe('Concurrent User Scenarios', () => {
    it('should handle concurrent login requests', async () => {
      (loginActions.loginWithMobilePassword as jest.Mock).mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve({ success: true }), 100))
      );

      renderComponent();
      fireEvent.click(screen.getByRole('button', { name: /mobile \+ password/i }));

      const concurrentUsers = 5;
      const loginPromises = [];

      for (let i = 0; i < concurrentUsers; i++) {
        loginPromises.push(
          loginActions.loginWithMobilePassword({
            mobile: `987654321${i}`,
            password: 'password123',
          })
        );
      }

      const results = await Promise.all(loginPromises);
      expect(results.every(r => r.success)).toBe(true);
      expect(loginActions.loginWithMobilePassword).toHaveBeenCalledTimes(concurrentUsers);
    });
  });
});